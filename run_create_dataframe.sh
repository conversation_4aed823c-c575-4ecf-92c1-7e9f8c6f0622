#!/bin/bash

# This script fixes the CREATE_DATAFRAME process by directly running the Python script
# with the correct paths to the processed hits and GTDB classification

# Set paths
RESULTS_DIR="../results_GCF_025823245"
GTDB_FILE="${RESULTS_DIR}/gtdbtk/gtdb_classification.csv"
CSV_HITS_DIR="${RESULTS_DIR}/processed_hits"
OUTPUT_FILE="${RESULTS_DIR}/final_results.csv"

echo "Starting CREATE_DATAFRAME fix script"
echo "Results directory: ${RESULTS_DIR}"
echo "GTDB classification file: ${GTDB_FILE}"
echo "CSV hits directory: ${CSV_HITS_DIR}"
echo "Output file: ${OUTPUT_FILE}"

# Verify the GTDB classification file exists
if [ ! -f "${GTDB_FILE}" ]; then
    echo "ERROR: GTDB classification file not found at ${GTDB_FILE}"
    exit 1
fi

# Verify the CSV hits directory exists
if [ ! -d "${CSV_HITS_DIR}" ]; then
    echo "ERROR: CSV hits directory not found at ${CSV_HITS_DIR}"
    exit 1
fi

# Run the script directly with the existing directory structure
echo "Running create_unique_df_hits_optimized.py..."
python ../create_unique_df_hits_optimized.py "${CSV_HITS_DIR}" "${GTDB_FILE}" "${OUTPUT_FILE}"

# Check if the script was successful
if [ $? -eq 0 ] && [ -f "${OUTPUT_FILE}" ]; then
    echo "Successfully created ${OUTPUT_FILE}"
    echo "CREATE_DATAFRAME process completed successfully!"
    exit 0
else
    echo "ERROR: Failed to create ${OUTPUT_FILE}"
    exit 1
fi
